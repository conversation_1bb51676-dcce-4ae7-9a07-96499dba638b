{"name": "cricket-booking-server", "version": "1.0.0", "description": "Cricket Ground & Room Booking System API", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cricket", "booking", "room", "ground", "api"], "author": "Cricket Association", "license": "MIT", "dependencies": {"@prisma/client": "^5.22.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "firebase-admin": "^13.2.0", "firebaseui": "^6.1.0", "mysql2": "^3.14.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.14.1", "prisma": "^5.22.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}