import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, authorize, auditLog } from '../middleware/auth';
import { CreateMealPlanRequest, UpdateMealPlanRequest } from '../types';
import { UserRole, MealType } from '@prisma/client';

const router = Router();
const prisma = new PrismaClient();

/**
 * GET /api/meals/plans
 * List meal plans
 */
router.get('/plans', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { date, mealType, startDate, endDate } = req.query;

    const where: any = {};
    
    if (date) {
      where.date = new Date(date as string);
    } else if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate as string),
        lte: new Date(endDate as string)
      };
    }
    
    if (mealType) where.mealType = mealType;

    const mealPlans = await prisma.mealPlan.findMany({
      where,
      orderBy: [
        { date: 'asc' },
        { mealType: 'asc' }
      ]
    });

    res.json({
      success: true,
      data: mealPlans
    });
  } catch (error) {
    console.error('Meal plans fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/meals/plans
 * Create meal plan (Admin only)
 */
router.post('/plans',
  authenticateToken,
  authorize([UserRole.ADMIN]),
  auditLog('CREATE', 'MEAL_PLAN'),
  async (req: Request, res: Response) => {
    try {
      const data: CreateMealPlanRequest = req.body;

      if (!data.date || !data.mealType || !data.menuItems || !data.estimatedQuantity) {
        return res.status(400).json({
          success: false,
          error: 'Date, meal type, menu items, and estimated quantity are required'
        });
      }

      if (!Object.values(MealType).includes(data.mealType)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid meal type'
        });
      }

      const mealPlan = await prisma.mealPlan.create({
        data: {
          date: new Date(data.date),
          mealType: data.mealType,
          menuItems: data.menuItems,
          estimatedQuantity: data.estimatedQuantity,
          costPerPerson: data.costPerPerson,
          notes: data.notes,
          isAutoGenerated: false
        }
      });

      res.status(201).json({
        success: true,
        data: mealPlan,
        message: 'Meal plan created successfully'
      });
    } catch (error) {
      console.error('Meal plan creation error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
);

/**
 * POST /api/meals/auto-generate
 * Auto-generate meal plans based on occupancy (Admin only)
 */
router.post('/auto-generate',
  authenticateToken,
  authorize([UserRole.ADMIN]),
  async (req: Request, res: Response) => {
    try {
      const { startDate, endDate } = req.body;

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          error: 'Start date and end date are required'
        });
      }

      // Get bookings for the date range to calculate occupancy
      const bookings = await prisma.booking.findMany({
        where: {
          status: { in: ['CONFIRMED'] },
          OR: [
            {
              startDate: { lte: new Date(endDate) },
              endDate: { gte: new Date(startDate) }
            }
          ]
        },
        select: {
          startDate: true,
          endDate: true,
          guestsCount: true
        }
      });

      // Calculate daily occupancy
      const dailyOccupancy: Record<string, number> = {};
      const start = new Date(startDate);
      const end = new Date(endDate);

      for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        const dateStr = date.toISOString().split('T')[0];
        dailyOccupancy[dateStr] = 0;

        bookings.forEach(booking => {
          if (booking.startDate <= date && booking.endDate >= date) {
            dailyOccupancy[dateStr] += booking.guestsCount;
          }
        });
      }

      // Generate meal plans
      const mealPlans = [];
      const mealTypes = Object.values(MealType);

      for (const [dateStr, occupancy] of Object.entries(dailyOccupancy)) {
        if (occupancy > 0) {
          for (const mealType of mealTypes) {
            // Check if meal plan already exists
            const existing = await prisma.mealPlan.findUnique({
              where: {
                unique_date_meal: {
                  date: new Date(dateStr),
                  mealType: mealType
                }
              }
            });

            if (!existing) {
              const mealPlan = await prisma.mealPlan.create({
                data: {
                  date: new Date(dateStr),
                  mealType: mealType,
                  menuItems: getDefaultMenuItems(mealType),
                  estimatedQuantity: occupancy,
                  costPerPerson: getDefaultCostPerPerson(mealType),
                  totalCost: occupancy * getDefaultCostPerPerson(mealType),
                  isAutoGenerated: true
                }
              });
              mealPlans.push(mealPlan);
            }
          }
        }
      }

      res.json({
        success: true,
        data: mealPlans,
        message: `Generated ${mealPlans.length} meal plans`
      });
    } catch (error) {
      console.error('Auto-generate meal plans error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
);

// Helper functions
function getDefaultMenuItems(mealType: MealType): string[] {
  const menuItems: Record<MealType, string[]> = {
    [MealType.BREAKFAST]: ['Tea/Coffee', 'Bread/Toast', 'Eggs', 'Fruits'],
    [MealType.LUNCH]: ['Rice', 'Dal', 'Vegetables', 'Roti', 'Salad'],
    [MealType.DINNER]: ['Rice', 'Dal', 'Vegetables', 'Roti', 'Dessert'],
    [MealType.SNACKS]: ['Tea/Coffee', 'Biscuits', 'Samosa', 'Fruits']
  };
  return menuItems[mealType] || [];
}

function getDefaultCostPerPerson(mealType: MealType): number {
  const costs: Record<MealType, number> = {
    [MealType.BREAKFAST]: 50,
    [MealType.LUNCH]: 100,
    [MealType.DINNER]: 120,
    [MealType.SNACKS]: 30
  };
  return costs[mealType] || 50;
}

/**
 * GET /api/meals/calendar
 * Get meal calendar view
 */
router.get('/calendar', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { start, end } = req.query;

    if (!start || !end) {
      return res.status(400).json({
        success: false,
        error: 'Start and end dates are required'
      });
    }

    const mealPlans = await prisma.mealPlan.findMany({
      where: {
        date: {
          gte: new Date(start as string),
          lte: new Date(end as string)
        }
      },
      orderBy: [
        { date: 'asc' },
        { mealType: 'asc' }
      ]
    });

    // Transform to calendar events
    const events = mealPlans.map(plan => ({
      id: plan.id,
      title: `${plan.mealType} - ${plan.estimatedQuantity} people`,
      start: plan.date,
      end: plan.date,
      type: 'meal',
      color: getMealTypeColor(plan.mealType),
      extendedProps: {
        mealPlan: plan,
        menuItems: plan.menuItems,
        estimatedQuantity: plan.estimatedQuantity,
        totalCost: plan.totalCost
      }
    }));

    res.json({
      success: true,
      data: events
    });
  } catch (error) {
    console.error('Meal calendar fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

function getMealTypeColor(mealType: MealType): string {
  const colors: Record<MealType, string> = {
    [MealType.BREAKFAST]: '#fbbf24',
    [MealType.LUNCH]: '#10b981',
    [MealType.DINNER]: '#3b82f6',
    [MealType.SNACKS]: '#8b5cf6'
  };
  return colors[mealType] || '#6b7280';
}

export default router;
