"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const client_2 = require("@prisma/client");
const router = (0, express_1.Router)();
const prisma = new client_1.PrismaClient();
/**
 * GET /api/meals/plans
 * List meal plans
 */
router.get('/plans', auth_1.authenticateToken, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { date, mealType, startDate, endDate } = req.query;
        const where = {};
        if (date) {
            where.date = new Date(date);
        }
        else if (startDate && endDate) {
            where.date = {
                gte: new Date(startDate),
                lte: new Date(endDate)
            };
        }
        if (mealType)
            where.mealType = mealType;
        const mealPlans = yield prisma.mealPlan.findMany({
            where,
            orderBy: [
                { date: 'asc' },
                { mealType: 'asc' }
            ]
        });
        res.json({
            success: true,
            data: mealPlans
        });
    }
    catch (error) {
        console.error('Meal plans fetch error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
}));
/**
 * POST /api/meals/plans
 * Create meal plan (Admin only)
 */
router.post('/plans', auth_1.authenticateToken, (0, auth_1.authorize)([client_2.UserRole.ADMIN]), (0, auth_1.auditLog)('CREATE', 'MEAL_PLAN'), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const data = req.body;
        if (!data.date || !data.mealType || !data.menuItems || !data.estimatedQuantity) {
            return res.status(400).json({
                success: false,
                error: 'Date, meal type, menu items, and estimated quantity are required'
            });
        }
        if (!Object.values(client_2.MealType).includes(data.mealType)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid meal type'
            });
        }
        const mealPlan = yield prisma.mealPlan.create({
            data: {
                date: new Date(data.date),
                mealType: data.mealType,
                menuItems: data.menuItems,
                estimatedQuantity: data.estimatedQuantity,
                costPerPerson: data.costPerPerson,
                notes: data.notes,
                isAutoGenerated: false
            }
        });
        res.status(201).json({
            success: true,
            data: mealPlan,
            message: 'Meal plan created successfully'
        });
    }
    catch (error) {
        console.error('Meal plan creation error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
}));
/**
 * POST /api/meals/auto-generate
 * Auto-generate meal plans based on occupancy (Admin only)
 */
router.post('/auto-generate', auth_1.authenticateToken, (0, auth_1.authorize)([client_2.UserRole.ADMIN]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { startDate, endDate } = req.body;
        if (!startDate || !endDate) {
            return res.status(400).json({
                success: false,
                error: 'Start date and end date are required'
            });
        }
        // Get bookings for the date range to calculate occupancy
        const bookings = yield prisma.booking.findMany({
            where: {
                status: { in: ['CONFIRMED'] },
                OR: [
                    {
                        startDate: { lte: new Date(endDate) },
                        endDate: { gte: new Date(startDate) }
                    }
                ]
            },
            select: {
                startDate: true,
                endDate: true,
                guestsCount: true
            }
        });
        // Calculate daily occupancy
        const dailyOccupancy = {};
        const start = new Date(startDate);
        const end = new Date(endDate);
        for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
            const dateStr = date.toISOString().split('T')[0];
            dailyOccupancy[dateStr] = 0;
            bookings.forEach(booking => {
                if (booking.startDate <= date && booking.endDate >= date) {
                    dailyOccupancy[dateStr] += booking.guestsCount;
                }
            });
        }
        // Generate meal plans
        const mealPlans = [];
        const mealTypes = Object.values(client_2.MealType);
        for (const [dateStr, occupancy] of Object.entries(dailyOccupancy)) {
            if (occupancy > 0) {
                for (const mealType of mealTypes) {
                    // Check if meal plan already exists
                    const existing = yield prisma.mealPlan.findUnique({
                        where: {
                            unique_date_meal: {
                                date: new Date(dateStr),
                                mealType: mealType
                            }
                        }
                    });
                    if (!existing) {
                        const mealPlan = yield prisma.mealPlan.create({
                            data: {
                                date: new Date(dateStr),
                                mealType: mealType,
                                menuItems: getDefaultMenuItems(mealType),
                                estimatedQuantity: occupancy,
                                costPerPerson: getDefaultCostPerPerson(mealType),
                                totalCost: occupancy * getDefaultCostPerPerson(mealType),
                                isAutoGenerated: true
                            }
                        });
                        mealPlans.push(mealPlan);
                    }
                }
            }
        }
        res.json({
            success: true,
            data: mealPlans,
            message: `Generated ${mealPlans.length} meal plans`
        });
    }
    catch (error) {
        console.error('Auto-generate meal plans error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
}));
// Helper functions
function getDefaultMenuItems(mealType) {
    const menuItems = {
        [client_2.MealType.BREAKFAST]: ['Tea/Coffee', 'Bread/Toast', 'Eggs', 'Fruits'],
        [client_2.MealType.LUNCH]: ['Rice', 'Dal', 'Vegetables', 'Roti', 'Salad'],
        [client_2.MealType.DINNER]: ['Rice', 'Dal', 'Vegetables', 'Roti', 'Dessert'],
        [client_2.MealType.SNACKS]: ['Tea/Coffee', 'Biscuits', 'Samosa', 'Fruits']
    };
    return menuItems[mealType] || [];
}
function getDefaultCostPerPerson(mealType) {
    const costs = {
        [client_2.MealType.BREAKFAST]: 50,
        [client_2.MealType.LUNCH]: 100,
        [client_2.MealType.DINNER]: 120,
        [client_2.MealType.SNACKS]: 30
    };
    return costs[mealType] || 50;
}
/**
 * GET /api/meals/calendar
 * Get meal calendar view
 */
router.get('/calendar', auth_1.authenticateToken, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { start, end } = req.query;
        if (!start || !end) {
            return res.status(400).json({
                success: false,
                error: 'Start and end dates are required'
            });
        }
        const mealPlans = yield prisma.mealPlan.findMany({
            where: {
                date: {
                    gte: new Date(start),
                    lte: new Date(end)
                }
            },
            orderBy: [
                { date: 'asc' },
                { mealType: 'asc' }
            ]
        });
        // Transform to calendar events
        const events = mealPlans.map(plan => ({
            id: plan.id,
            title: `${plan.mealType} - ${plan.estimatedQuantity} people`,
            start: plan.date,
            end: plan.date,
            type: 'meal',
            color: getMealTypeColor(plan.mealType),
            extendedProps: {
                mealPlan: plan,
                menuItems: plan.menuItems,
                estimatedQuantity: plan.estimatedQuantity,
                totalCost: plan.totalCost
            }
        }));
        res.json({
            success: true,
            data: events
        });
    }
    catch (error) {
        console.error('Meal calendar fetch error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
}));
function getMealTypeColor(mealType) {
    const colors = {
        [client_2.MealType.BREAKFAST]: '#fbbf24',
        [client_2.MealType.LUNCH]: '#10b981',
        [client_2.MealType.DINNER]: '#3b82f6',
        [client_2.MealType.SNACKS]: '#8b5cf6'
    };
    return colors[mealType] || '#6b7280';
}
exports.default = router;
