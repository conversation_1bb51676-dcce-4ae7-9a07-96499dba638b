// Core type definitions for the Cricket Ground & Room Booking System

export interface User {
  id: string;
  firebaseUid: string;
  email: string;
  name: string;
  phone?: string;
  role: UserRole;
  avatarUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  HOUSEKEEPING = 'HOUSEKEEPING',
  USER = 'USER'
}

export interface Facility {
  id: string;
  name: string;
  type: FacilityType;
  description?: string;
  location?: string;
  state?: string;
  city?: string;
  address?: string;
  capacity?: number;
  amenities?: string[];
  images?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  rooms?: Room[];
}

export enum FacilityType {
  GROUND = 'GROUND',
  BUILDING = 'BUILDING'
}

export interface Room {
  id: string;
  facilityId: string;
  name: string;
  roomNumber?: string;
  type: RoomType;
  capacity: number;
  floorNumber?: number;
  amenities?: string[];
  images?: string[];
  pricePerNight?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  facility?: Facility;
}

export enum RoomType {
  SINGLE = 'SINGLE',
  DOUBLE = 'DOUBLE',
  SUITE = 'SUITE',
  DORMITORY = 'DORMITORY'
}

export interface Booking {
  id: string;
  userId: string;
  facilityId?: string;
  roomId?: string;
  bookingType: BookingType;
  startDate: Date;
  endDate: Date;
  startTime?: Date;
  endTime?: Date;
  guestsCount: number;
  totalAmount?: number;
  status: BookingStatus;
  specialRequests?: string;
  createdAt: Date;
  updatedAt: Date;
  user?: User;
  facility?: Facility;
  room?: Room;
}

export enum BookingType {
  GROUND = 'GROUND',
  ROOM = 'ROOM'
}

export enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

export interface HousekeepingTask {
  id: string;
  roomId: string;
  assignedTo: string;
  taskType: HousekeepingTaskType;
  priority: TaskPriority;
  status: HousekeepingTaskStatus;
  description?: string;
  deadline?: Date;
  completionNotes?: string;
  images?: string[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  room?: Room;
  assignedUser?: User;
}

export enum HousekeepingTaskType {
  CLEANING = 'CLEANING',
  MAINTENANCE = 'MAINTENANCE',
  INSPECTION = 'INSPECTION'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum HousekeepingTaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  NEEDS_REPAIR = 'NEEDS_REPAIR'
}

export interface MealPlan {
  id: string;
  date: Date;
  mealType: MealType;
  menuItems: string[];
  estimatedQuantity: number;
  actualQuantity?: number;
  costPerPerson?: number;
  totalCost?: number;
  notes?: string;
  isAutoGenerated: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum MealType {
  BREAKFAST = 'BREAKFAST',
  LUNCH = 'LUNCH',
  DINNER = 'DINNER',
  SNACKS = 'SNACKS'
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  isRead: boolean;
  isPushSent: boolean;
  relatedId?: string;
  relatedType?: string;
  createdAt: Date;
  user?: User;
}

export enum NotificationType {
  BOOKING = 'BOOKING',
  HOUSEKEEPING = 'HOUSEKEEPING',
  MEAL = 'MEAL',
  SYSTEM = 'SYSTEM'
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH'
}

export interface PushToken {
  id: string;
  userId: string;
  token: string;
  platform: Platform;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum Platform {
  WEB = 'WEB',
  IOS = 'IOS',
  ANDROID = 'ANDROID'
}

// API Request/Response types
export interface CreateFacilityRequest {
  name: string;
  type: FacilityType;
  description?: string;
  location?: string;
  state?: string;
  city?: string;
  address?: string;
  capacity?: number;
  amenities?: string[];
  images?: string[];
}

export interface UpdateFacilityRequest extends Partial<CreateFacilityRequest> {
  isActive?: boolean;
}

export interface CreateRoomRequest {
  name: string;
  roomNumber?: string;
  type: RoomType;
  capacity: number;
  floorNumber?: number;
  amenities?: string[];
  images?: string[];
  pricePerNight?: number;
}

export interface UpdateRoomRequest extends Partial<CreateRoomRequest> {
  isActive?: boolean;
}

export interface CreateBookingRequest {
  facilityId?: string;
  roomId?: string;
  bookingType: BookingType;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  guestsCount: number;
  specialRequests?: string;
}

export interface UpdateBookingRequest extends Partial<CreateBookingRequest> {
  status?: BookingStatus;
}

export interface CreateHousekeepingTaskRequest {
  roomId: string;
  assignedTo: string;
  taskType: HousekeepingTaskType;
  priority: TaskPriority;
  description?: string;
  deadline?: string;
}

export interface UpdateHousekeepingTaskRequest extends Partial<CreateHousekeepingTaskRequest> {
  status?: HousekeepingTaskStatus;
  completionNotes?: string;
  images?: string[];
}

export interface CreateMealPlanRequest {
  date: string;
  mealType: MealType;
  menuItems: string[];
  estimatedQuantity: number;
  costPerPerson?: number;
  notes?: string;
}

export interface UpdateMealPlanRequest extends Partial<CreateMealPlanRequest> {
  actualQuantity?: number;
  totalCost?: number;
}

export interface CreateNotificationRequest {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  relatedId?: string;
  relatedType?: string;
}

// Filter and query types
export interface FacilityFilters {
  type?: FacilityType;
  state?: string;
  city?: string;
  isActive?: boolean;
  search?: string;
}

export interface BookingFilters {
  userId?: string;
  facilityId?: string;
  roomId?: string;
  bookingType?: BookingType;
  status?: BookingStatus;
  startDate?: string;
  endDate?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface HousekeepingTaskFilters {
  assignedTo?: string;
  roomId?: string;
  taskType?: HousekeepingTaskType;
  priority?: TaskPriority;
  status?: HousekeepingTaskStatus;
  deadline?: string;
}

export interface MealPlanFilters {
  date?: string;
  mealType?: MealType;
  dateRange?: {
    start: string;
    end: string;
  };
}

// Pagination
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

// Calendar view types
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  type: 'booking' | 'task' | 'meal';
  status?: string;
  color?: string;
  extendedProps?: Record<string, any>;
}

export interface AvailabilitySlot {
  date: string;
  isAvailable: boolean;
  bookings?: Booking[];
  reason?: string;
}

// Report types
export interface UsageReport {
  facilityId: string;
  facilityName: string;
  totalBookings: number;
  totalRevenue: number;
  occupancyRate: number;
  averageStayDuration: number;
  period: {
    start: Date;
    end: Date;
  };
}

export interface BookingReport {
  totalBookings: number;
  confirmedBookings: number;
  cancelledBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  topFacilities: Array<{
    facilityId: string;
    facilityName: string;
    bookingCount: number;
  }>;
  period: {
    start: Date;
    end: Date;
  };
}

export interface HousekeepingReport {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  overdueeTasks: number;
  averageCompletionTime: number;
  tasksByPriority: Record<TaskPriority, number>;
  tasksByType: Record<HousekeepingTaskType, number>;
  period: {
    start: Date;
    end: Date;
  };
}
