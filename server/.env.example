# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/cricket_booking"

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_ADMIN_SDK_PATH=./config/firebase-adminsdk.json

# File Upload Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Cloudinary Configuration (Alternative to AWS)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://localhost:6379

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Push Notification Configuration
FCM_SERVER_KEY=your-fcm-server-key
