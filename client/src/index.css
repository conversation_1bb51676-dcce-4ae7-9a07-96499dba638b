@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Background/Base - Light Gray */
    --background: 210 17% 96%; /* #F4F6F7 */
    --foreground: 210 29% 24%; /* #2C3E50 - Primary Text */

    /* Card Colors */
    --card: 0 0% 100%; /* White */
    --card-foreground: 210 29% 24%; /* #2C3E50 */

    /* Popover Colors */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 210 29% 24%; /* #2C3E50 */

    /* Primary Color - Cricket Green #27AE60 */
    --primary: 145 63% 42%; /* #27AE60 */
    --primary-foreground: 0 0% 100%; /* White */

    /* Secondary Color - Navy Blue #2C3E50 */
    --secondary: 210 29% 24%; /* #2C3E50 */
    --secondary-foreground: 0 0% 100%; /* White */

    /* Muted Colors - Soft Gray */
    --muted: 200 18% 46%; /* #7F8C8D */
    --muted-foreground: 210 29% 24%; /* #2C3E50 */

    /* Accent Color - Orange #F39C12 */
    --accent: 35 81% 52%; /* #F39C12 */
    --accent-foreground: 210 29% 24%; /* #2C3E50 */

    /* Status Colors */
    --destructive: 0 84% 60%; /* Red for errors */
    --destructive-foreground: 0 0% 100%; /* White */

    /* Border and Input */
    --border: 210 14% 83%; /* #DEE2E6 */
    --input: 210 14% 83%; /* #DEE2E6 */
    --ring: 145 63% 42%; /* #27AE60 - Focus ring */
    --radius: 0.5rem;
  }

  .dark {
    /* Dark Background */
    --background: 210 29% 24%; /* #2C3E50 - Navy Blue */
    --foreground: 210 17% 96%; /* #F4F6F7 - Light Gray */

    /* Dark Card Colors */
    --card: 210 25% 20%; /* Darker Navy */
    --card-foreground: 210 17% 96%; /* Light Gray */

    /* Dark Popover Colors */
    --popover: 210 25% 20%; /* Darker Navy */
    --popover-foreground: 210 17% 96%; /* Light Gray */

    /* Primary Color - Cricket Green (same) */
    --primary: 145 63% 42%; /* #27AE60 */
    --primary-foreground: 0 0% 100%; /* White */

    /* Secondary Color - Lighter Navy for contrast */
    --secondary: 210 25% 30%; /* Lighter Navy */
    --secondary-foreground: 210 17% 96%; /* Light Gray */

    /* Dark Muted Colors */
    --muted: 210 25% 30%; /* Lighter Navy */
    --muted-foreground: 200 18% 60%; /* Lighter Gray */

    /* Accent Color - Orange (same) */
    --accent: 35 81% 52%; /* #F39C12 */
    --accent-foreground: 210 29% 24%; /* Navy Blue */

    /* Dark Status Colors */
    --destructive: 0 84% 60%; /* Red for errors */
    --destructive-foreground: 0 0% 100%; /* White */

    /* Dark Border and Input */
    --border: 210 25% 30%; /* Lighter Navy */
    --input: 210 25% 30%; /* Lighter Navy */
    --ring: 145 63% 42%; /* #27AE60 - Focus ring */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    margin: 0;
    min-height: 100vh;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Calendar styles */
.rbc-calendar {
  @apply bg-background text-foreground;
}

.rbc-header {
  @apply bg-muted text-muted-foreground font-medium py-2;
}

.rbc-today {
  @apply bg-primary/10;
}

.rbc-event {
  @apply bg-primary text-primary-foreground rounded-md;
}

.rbc-selected {
  @apply bg-primary/20;
}

/* Loading spinner */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

/* Form styles */
.form-error {
  @apply text-destructive text-sm mt-1;
}

.form-label {
  @apply text-sm font-medium text-foreground mb-2 block;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-[1.02];
}

/* Status badges */
.status-pending {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

.status-confirmed {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.status-cancelled {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

.status-completed {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

/* Priority badges */
.priority-low {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
}

.priority-medium {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

.priority-high {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300;
}

.priority-urgent {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}
