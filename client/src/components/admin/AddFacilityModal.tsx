import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';
import { X, Upload, Plus, Trash2 } from 'lucide-react';

interface AddFacilityModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (facility: any) => void;
  editingFacility?: any;
}

const AddFacilityModal: React.FC<AddFacilityModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingFacility
}) => {
  const [formData, setFormData] = useState({
    name: editingFacility?.name || '',
    type: editingFacility?.type || 'ROOM',
    description: editingFacility?.description || '',
    capacity: editingFacility?.capacity || '',
    location: editingFacility?.location || '',
    pricePerHour: editingFacility?.pricePerHour || '',
    amenities: editingFacility?.amenities || [],
    parentId: editingFacility?.parentId || '',
    isActive: editingFacility?.isActive ?? true
  });

  const [newAmenity, setNewAmenity] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const commonAmenities = [
    'WiFi', 'AC', 'Parking', 'Projector', 'Whiteboard', 'Sound System',
    'Catering', 'Security', 'Elevator', 'Restrooms', 'Floodlights',
    'Pavilion', 'Scoreboard', 'Changing Rooms', 'First Aid'
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!formData.capacity || formData.capacity <= 0) {
      newErrors.capacity = 'Valid capacity is required';
    }
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }
    if (formData.type !== 'BUILDING' && (!formData.pricePerHour || formData.pricePerHour < 0)) {
      newErrors.pricePerHour = 'Valid price is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave({
        ...formData,
        capacity: parseInt(formData.capacity),
        pricePerHour: parseFloat(formData.pricePerHour) || 0,
        id: editingFacility?.id || Date.now().toString(),
        createdAt: editingFacility?.createdAt || new Date().toISOString(),
        images: editingFacility?.images || []
      });
      onClose();
    }
  };

  const addAmenity = (amenity: string) => {
    if (amenity && !formData.amenities.includes(amenity)) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, amenity]
      }));
    }
    setNewAmenity('');
  };

  const removeAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter(a => a !== amenity)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="relative bg-background rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-none">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>
                {editingFacility ? 'Edit Facility' : 'Add New Facility'}
              </CardTitle>
              <CardDescription>
                {editingFacility ? 'Update facility information' : 'Create a new facility for booking'}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="form-label">Name *</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="Enter facility name"
                  />
                  {errors.name && <p className="form-error">{errors.name}</p>}
                </div>

                <div>
                  <label className="form-label">Type *</label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                  >
                    <option value="CRICKET_GROUND">Cricket Ground</option>
                    <option value="BUILDING">Building</option>
                    <option value="ROOM">Room</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="form-label">Description *</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                  placeholder="Enter facility description"
                />
                {errors.description && <p className="form-error">{errors.description}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="form-label">Capacity *</label>
                  <input
                    type="number"
                    value={formData.capacity}
                    onChange={(e) => setFormData(prev => ({ ...prev, capacity: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="Max people"
                    min="1"
                  />
                  {errors.capacity && <p className="form-error">{errors.capacity}</p>}
                </div>

                <div>
                  <label className="form-label">Location *</label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="Building, Floor, etc."
                  />
                  {errors.location && <p className="form-error">{errors.location}</p>}
                </div>

                <div>
                  <label className="form-label">
                    Price per Hour {formData.type !== 'BUILDING' && '*'}
                  </label>
                  <input
                    type="number"
                    value={formData.pricePerHour}
                    onChange={(e) => setFormData(prev => ({ ...prev, pricePerHour: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="₹ 0"
                    min="0"
                    step="0.01"
                    disabled={formData.type === 'BUILDING'}
                  />
                  {errors.pricePerHour && <p className="form-error">{errors.pricePerHour}</p>}
                </div>
              </div>

              {/* Amenities */}
              <div>
                <label className="form-label">Amenities</label>
                
                {/* Quick Add Buttons */}
                <div className="flex flex-wrap gap-2 mb-3">
                  {commonAmenities.filter(a => !formData.amenities.includes(a)).map(amenity => (
                    <button
                      key={amenity}
                      type="button"
                      onClick={() => addAmenity(amenity)}
                      className="px-3 py-1 text-xs border border-input rounded-md hover:bg-accent transition-colors"
                    >
                      + {amenity}
                    </button>
                  ))}
                </div>

                {/* Custom Amenity Input */}
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={newAmenity}
                    onChange={(e) => setNewAmenity(e.target.value)}
                    className="flex-1 px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                    placeholder="Add custom amenity"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity(newAmenity))}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => addAmenity(newAmenity)}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                {/* Selected Amenities */}
                <div className="flex flex-wrap gap-2">
                  {formData.amenities.map(amenity => (
                    <div
                      key={amenity}
                      className="flex items-center gap-1 px-3 py-1 bg-primary/10 text-primary rounded-md text-sm"
                    >
                      {amenity}
                      <button
                        type="button"
                        onClick={() => removeAmenity(amenity)}
                        className="ml-1 hover:text-destructive"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="rounded border-input"
                />
                <label htmlFor="isActive" className="text-sm font-medium">
                  Active (available for booking)
                </label>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-primary hover:bg-primary/90">
                  {editingFacility ? 'Update Facility' : 'Create Facility'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddFacilityModal;
