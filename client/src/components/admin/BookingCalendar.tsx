import React, { useState, useMemo } from 'react';
import { Calendar, momentLocalizer, Views } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';
import { Plus, Filter, Eye, Edit, Trash2 } from 'lucide-react';

const localizer = momentLocalizer(moment);

interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  resource: {
    facilityId: string;
    facilityName: string;
    facilityType: string;
    userId: string;
    userName: string;
    status: string;
    guestCount: number;
    totalAmount: number;
    specialRequests?: string;
  };
}

interface BookingCalendarProps {
  bookings: any[];
  onEventSelect?: (event: CalendarEvent) => void;
  onSlotSelect?: (slotInfo: any) => void;
  onEventUpdate?: (event: CalendarEvent) => void;
}

const BookingCalendar: React.FC<BookingCalendarProps> = ({
  bookings,
  onEventSelect,
  onSlotSelect,
  onEventUpdate
}) => {
  const [view, setView] = useState<any>(Views.MONTH);
  const [date, setDate] = useState(new Date());
  const [selectedFacility, setSelectedFacility] = useState<string>('ALL');

  // Convert bookings to calendar events
  const events: CalendarEvent[] = useMemo(() => {
    return bookings
      .filter(booking => selectedFacility === 'ALL' || booking.facilityId === selectedFacility)
      .map(booking => ({
        id: booking.id,
        title: `${booking.facilityName} - ${booking.userName}`,
        start: new Date(booking.startTime),
        end: new Date(booking.endTime),
        resource: {
          facilityId: booking.facilityId,
          facilityName: booking.facilityName,
          facilityType: booking.facilityType,
          userId: booking.userId,
          userName: booking.userName,
          status: booking.status,
          guestCount: booking.guestCount,
          totalAmount: booking.totalAmount,
          specialRequests: booking.specialRequests
        }
      }));
  }, [bookings, selectedFacility]);

  // Get unique facilities for filter
  const facilities = useMemo(() => {
    const uniqueFacilities = Array.from(
      new Set(bookings.map(b => `${b.facilityId}:${b.facilityName}`))
    ).map(item => {
      const [id, name] = item.split(':');
      return { id, name };
    });
    return uniqueFacilities;
  }, [bookings]);

  // Event style getter
  const eventStyleGetter = (event: CalendarEvent) => {
    let backgroundColor = '#3174ad';
    let borderColor = '#3174ad';
    
    switch (event.resource.status) {
      case 'PENDING':
        backgroundColor = '#f59e0b';
        borderColor = '#d97706';
        break;
      case 'CONFIRMED':
        backgroundColor = '#10b981';
        borderColor = '#059669';
        break;
      case 'CANCELLED':
        backgroundColor = '#ef4444';
        borderColor = '#dc2626';
        break;
      case 'COMPLETED':
        backgroundColor = '#6366f1';
        borderColor = '#4f46e5';
        break;
    }

    return {
      style: {
        backgroundColor,
        borderColor,
        color: 'white',
        border: `2px solid ${borderColor}`,
        borderRadius: '4px',
        fontSize: '12px',
        padding: '2px 4px'
      }
    };
  };

  // Custom event component
  const EventComponent = ({ event }: { event: CalendarEvent }) => (
    <div className="text-xs">
      <div className="font-medium truncate">{event.resource.facilityName}</div>
      <div className="truncate">{event.resource.userName}</div>
      <div>{event.resource.guestCount} guests</div>
    </div>
  );

  // Custom toolbar
  const CustomToolbar = ({ label, onNavigate, onView }: any) => (
    <div className="flex flex-col md:flex-row justify-between items-center mb-4 gap-4">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('PREV')}
        >
          Previous
        </Button>
        <span className="text-lg font-semibold px-4">{label}</span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('NEXT')}
        >
          Next
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('TODAY')}
        >
          Today
        </Button>
      </div>
      
      <div className="flex items-center gap-2">
        <select
          value={selectedFacility}
          onChange={(e) => setSelectedFacility(e.target.value)}
          className="px-3 py-1 border border-input rounded-md bg-background text-sm"
        >
          <option value="ALL">All Facilities</option>
          {facilities.map(facility => (
            <option key={facility.id} value={facility.id}>
              {facility.name}
            </option>
          ))}
        </select>
        
        <div className="flex border border-input rounded-md">
          <Button
            variant={view === Views.MONTH ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onView(Views.MONTH)}
            className="rounded-r-none"
          >
            Month
          </Button>
          <Button
            variant={view === Views.WEEK ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onView(Views.WEEK)}
            className="rounded-none border-x-0"
          >
            Week
          </Button>
          <Button
            variant={view === Views.DAY ? 'default' : 'ghost'}
            size="sm"
            onClick={() => onView(Views.DAY)}
            className="rounded-l-none"
          >
            Day
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Booking Calendar</CardTitle>
            <CardDescription>
              Interactive calendar view of all facility bookings
            </CardDescription>
          </div>
          <Button onClick={() => onSlotSelect && onSlotSelect(null)}>
            <Plus className="w-4 h-4 mr-2" />
            New Booking
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Legend */}
        <div className="flex flex-wrap gap-4 mb-4 p-3 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-yellow-500 rounded"></div>
            <span className="text-sm">Pending</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-500 rounded"></div>
            <span className="text-sm">Confirmed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-red-500 rounded"></div>
            <span className="text-sm">Cancelled</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-indigo-500 rounded"></div>
            <span className="text-sm">Completed</span>
          </div>
        </div>

        {/* Calendar */}
        <div style={{ height: '600px' }}>
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            view={view}
            onView={setView}
            date={date}
            onNavigate={setDate}
            eventPropGetter={eventStyleGetter}
            components={{
              event: EventComponent,
              toolbar: CustomToolbar
            }}
            onSelectEvent={onEventSelect}
            onSelectSlot={onSlotSelect}
            selectable
            popup
            showMultiDayTimes
            step={30}
            timeslots={2}
            defaultView={Views.MONTH}
            views={[Views.MONTH, Views.WEEK, Views.DAY]}
            formats={{
              timeGutterFormat: 'HH:mm',
              eventTimeRangeFormat: ({ start, end }) => 
                `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`,
              dayHeaderFormat: 'ddd DD/MM',
              dayRangeHeaderFormat: ({ start, end }) =>
                `${moment(start).format('DD/MM')} - ${moment(end).format('DD/MM')}`
            }}
            messages={{
              next: 'Next',
              previous: 'Previous',
              today: 'Today',
              month: 'Month',
              week: 'Week',
              day: 'Day',
              agenda: 'Agenda',
              date: 'Date',
              time: 'Time',
              event: 'Event',
              noEventsInRange: 'No bookings in this date range',
              showMore: (total) => `+${total} more`
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default BookingCalendar;
