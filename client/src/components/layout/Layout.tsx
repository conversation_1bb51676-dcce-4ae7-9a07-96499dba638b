import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';
import Button from '../ui/Button';
import { BarChart3, Building, Calendar, CheckSquare, Clock, TrendingUp, Menu, X, LogOut } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const getIcon = (iconName: string) => {
    const iconProps = { className: "w-5 h-5" };
    switch (iconName) {
      case 'BarChart3': return <BarChart3 {...iconProps} />;
      case 'Building': return <Building {...iconProps} />;
      case 'Calendar': return <Calendar {...iconProps} />;
      case 'CheckSquare': return <CheckSquare {...iconProps} />;
      case 'Clock': return <Clock {...iconProps} />;
      case 'TrendingUp': return <TrendingUp {...iconProps} />;
      default: return <BarChart3 {...iconProps} />;
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: 'BarChart3', roles: [UserRole.ADMIN, UserRole.HOUSEKEEPING, UserRole.USER] },
    { name: 'Facilities', href: '/facilities', icon: 'Building', roles: [UserRole.ADMIN, UserRole.HOUSEKEEPING, UserRole.USER] },
    { name: 'Bookings', href: '/bookings', icon: 'Calendar', roles: [UserRole.ADMIN, UserRole.HOUSEKEEPING, UserRole.USER] },
    { name: 'Housekeeping', href: '/housekeeping', icon: 'CheckSquare', roles: [UserRole.ADMIN, UserRole.HOUSEKEEPING] },
    { name: 'Meals', href: '/meals', icon: 'Clock', roles: [UserRole.ADMIN, UserRole.HOUSEKEEPING, UserRole.USER] },
    { name: 'Reports', href: '/reports', icon: 'TrendingUp', roles: [UserRole.ADMIN] },
  ];

  const adminNavigation = [
    { name: 'Manage Facilities', href: '/admin/facilities', icon: 'Building', roles: [UserRole.ADMIN] },
    { name: 'Manage Bookings', href: '/admin/bookings', icon: 'Calendar', roles: [UserRole.ADMIN] },
    { name: 'Assign Tasks', href: '/admin/housekeeping', icon: 'CheckSquare', roles: [UserRole.ADMIN] },
  ];

  const filteredNavigation = navigation.filter(item =>
    item.roles.includes(user?.role || UserRole.USER)
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-card border-r transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:static lg:inset-0`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-2">
                <span className="text-white font-bold text-sm">CG</span>
              </div>
              <h1 className="text-xl font-bold">Cricket Booking</h1>
            </div>
            <button
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {filteredNavigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  location.pathname === item.href
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
              >
                <span className="mr-3">{getIcon(item.icon)}</span>
                {item.name}
              </Link>
            ))}

            {/* Admin Section */}
            {user?.role === UserRole.ADMIN && (
              <>
                <div className="pt-4 pb-2">
                  <h3 className="px-4 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                    Administration
                  </h3>
                </div>
                {adminNavigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      location.pathname === item.href
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }`}
                  >
                    <span className="mr-3">{getIcon(item.icon)}</span>
                    {item.name}
                  </Link>
                ))}
              </>
            )}
          </nav>

          {/* User menu */}
          <div className="p-4 border-t">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
                {user?.name?.charAt(0).toUpperCase()}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{user?.name}</p>
                <p className="text-xs text-muted-foreground">{user?.role}</p>
              </div>
            </div>
            <div className="space-y-2">
              <Link
                to="/profile"
                className="block w-full text-left px-3 py-2 text-sm text-muted-foreground hover:bg-accent hover:text-accent-foreground rounded-md"
              >
                Profile
              </Link>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="w-full justify-start"
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-background border-b">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="w-6 h-6" />
            </button>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                Welcome back, {user?.name}
              </span>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
