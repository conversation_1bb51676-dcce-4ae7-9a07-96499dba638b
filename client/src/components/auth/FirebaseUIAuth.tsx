import React, { useEffect, useRef } from 'react';
import { getAuth, EmailAuthProvider, GoogleAuthProvider } from 'firebase/auth';
import * as firebaseui from 'firebaseui';
import 'firebaseui/dist/firebaseui.css';

interface FirebaseUIAuthProps {
  onSignInSuccess?: (user: any) => void;
  onSignInFailure?: (error: any) => void;
}

const FirebaseUIAuth: React.FC<FirebaseUIAuthProps> = ({
  onSignInSuccess,
  onSignInFailure
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const uiRef = useRef<firebaseui.auth.AuthUI | null>(null);

  useEffect(() => {
    // Get Firebase Auth instance
    const auth = getAuth();
    
    // Check if Firebase is configured
    if (!auth.app.options.apiKey) {
      console.warn('Firebase not configured, Firebase UI will not work');
      return;
    }

    // Initialize Firebase UI
    if (!uiRef.current) {
      uiRef.current = new firebaseui.auth.AuthUI(auth);
    }

    const uiConfig: firebaseui.auth.Config = {
      signInFlow: 'popup',
      signInOptions: [
        // Email/Password sign-in
        {
          provider: EmailAuthProvider.PROVIDER_ID,
          requireDisplayName: true,
          signInMethod: EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD,
        },
        // Google sign-in
        {
          provider: GoogleAuthProvider.PROVIDER_ID,
          scopes: ['profile', 'email'],
          customParameters: {
            prompt: 'select_account'
          }
        }
      ],
      signInSuccessUrl: '/dashboard',
      callbacks: {
        signInSuccessWithAuthResult: (authResult: any) => {
          // Handle successful sign-in
          if (onSignInSuccess) {
            onSignInSuccess(authResult.user);
          }
          // Return false to avoid redirect, we'll handle it manually
          return false;
        },
        signInFailure: (error: any) => {
          // Handle sign-in failure
          if (onSignInFailure) {
            onSignInFailure(error);
          }
          return Promise.resolve();
        }
      },
      tosUrl: '/terms',
      privacyPolicyUrl: '/privacy'
    };

    // Start Firebase UI
    if (elementRef.current) {
      uiRef.current.start(elementRef.current, uiConfig);
    }

    // Cleanup function
    return () => {
      if (uiRef.current) {
        uiRef.current.reset();
      }
    };
  }, [onSignInSuccess, onSignInFailure]);

  return (
    <div className="firebase-ui-container">
      <div ref={elementRef} />
    </div>
  );
};

export default FirebaseUIAuth;
