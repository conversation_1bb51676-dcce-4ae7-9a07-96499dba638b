// User types
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: UserRole;
  avatarUrl?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  HOUSEKEEPING = 'HOUSEKEEPING',
  USER = 'USER'
}

// Facility types
export interface Facility {
  id: string;
  name: string;
  type: FacilityType;
  description?: string;
  location?: string;
  state?: string;
  city?: string;
  address?: string;
  capacity?: number;
  amenities?: string[];
  images?: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  rooms?: Room[];
  _count?: {
    rooms: number;
    bookings: number;
  };
}

export enum FacilityType {
  GROUND = 'GROUND',
  BUILDING = 'BUILDING'
}

export interface Room {
  id: string;
  facilityId: string;
  name: string;
  roomNumber?: string;
  type: RoomType;
  capacity: number;
  floorNumber?: number;
  amenities?: string[];
  images?: string[];
  pricePerNight?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  facility?: Facility;
  _count?: {
    bookings: number;
    housekeepingTasks: number;
  };
}

export enum RoomType {
  SINGLE = 'SINGLE',
  DOUBLE = 'DOUBLE',
  SUITE = 'SUITE',
  DORMITORY = 'DORMITORY'
}

// Booking types
export interface Booking {
  id: string;
  userId: string;
  facilityId?: string;
  roomId?: string;
  bookingType: BookingType;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  guestsCount: number;
  totalAmount?: number;
  status: BookingStatus;
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
  user?: User;
  facility?: Facility;
  room?: Room;
}

export enum BookingType {
  GROUND = 'GROUND',
  ROOM = 'ROOM'
}

export enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

// Housekeeping types
export interface HousekeepingTask {
  id: string;
  roomId: string;
  assignedTo: string;
  taskType: HousekeepingTaskType;
  priority: TaskPriority;
  status: HousekeepingTaskStatus;
  description?: string;
  deadline?: string;
  completionNotes?: string;
  images?: string[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  room?: Room;
  assignedUser?: User;
}

export enum HousekeepingTaskType {
  CLEANING = 'CLEANING',
  MAINTENANCE = 'MAINTENANCE',
  INSPECTION = 'INSPECTION'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum HousekeepingTaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  NEEDS_REPAIR = 'NEEDS_REPAIR'
}

// Meal types
export interface MealPlan {
  id: string;
  date: string;
  mealType: MealType;
  menuItems: string[];
  estimatedQuantity: number;
  actualQuantity?: number;
  costPerPerson?: number;
  totalCost?: number;
  notes?: string;
  isAutoGenerated: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum MealType {
  BREAKFAST = 'BREAKFAST',
  LUNCH = 'LUNCH',
  DINNER = 'DINNER',
  SNACKS = 'SNACKS'
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  isRead: boolean;
  isPushSent: boolean;
  relatedId?: string;
  relatedType?: string;
  createdAt: string;
}

export enum NotificationType {
  BOOKING = 'BOOKING',
  HOUSEKEEPING = 'HOUSEKEEPING',
  MEAL = 'MEAL',
  SYSTEM = 'SYSTEM'
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH'
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Form types
export interface CreateFacilityForm {
  name: string;
  type: FacilityType;
  description?: string;
  location?: string;
  state?: string;
  city?: string;
  address?: string;
  capacity?: number;
  amenities?: string[];
  images?: string[];
}

export interface CreateRoomForm {
  name: string;
  roomNumber?: string;
  type: RoomType;
  capacity: number;
  floorNumber?: number;
  amenities?: string[];
  images?: string[];
  pricePerNight?: number;
}

export interface CreateBookingForm {
  facilityId?: string;
  roomId?: string;
  bookingType: BookingType;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  guestsCount: number;
  specialRequests?: string;
}

export interface CreateHousekeepingTaskForm {
  roomId: string;
  assignedTo: string;
  taskType: HousekeepingTaskType;
  priority: TaskPriority;
  description?: string;
  deadline?: string;
}

export interface CreateMealPlanForm {
  date: string;
  mealType: MealType;
  menuItems: string[];
  estimatedQuantity: number;
  costPerPerson?: number;
  notes?: string;
}

// Calendar types
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  type: 'booking' | 'task' | 'meal';
  status?: string;
  color?: string;
  extendedProps?: Record<string, any>;
}
