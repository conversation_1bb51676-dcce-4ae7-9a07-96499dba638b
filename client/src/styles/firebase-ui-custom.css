/* Custom Firebase UI Styles - Cricket Ground Theme */

/* Container styling */
.firebase-ui-container {
  width: 100%;
}

/* Main container */
.firebaseui-container {
  background: transparent !important;
  box-shadow: none !important;
  max-width: none !important;
  width: 100% !important;
}

/* Card styling */
.firebaseui-card-content {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
}

.firebaseui-card-header {
  padding: 0 0 1.5rem 0 !important;
  border-bottom: none !important;
}

.firebaseui-card-actions {
  padding: 1.5rem 0 0 0 !important;
}

/* Title styling - Cricket Green Theme */
.firebaseui-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #27AE60 !important; /* Cricket Green */
  text-align: center !important;
  margin: 0 0 0.5rem 0 !important;
}

.firebaseui-subtitle {
  color: #7F8C8D !important; /* Soft Gray */
  text-align: center !important;
  font-size: 0.875rem !important;
  margin: 0 0 1.5rem 0 !important;
}

/* Button styling - Cricket Theme */
.firebaseui-idp-button {
  border-radius: 0.5rem !important;
  border: 2px solid #DEE2E6 !important; /* Light border */
  background: #FFFFFF !important; /* White background */
  color: #2C3E50 !important; /* Navy text */
  font-weight: 600 !important;
  height: 3rem !important;
  margin: 0.75rem 0 !important;
  transition: all 0.3s ease-in-out !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.firebaseui-idp-button:hover {
  background: #F39C12 !important; /* Orange accent on hover */
  color: #2C3E50 !important; /* Navy text */
  border-color: #F39C12 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

/* Google button specific styling */
.firebaseui-idp-google > .firebaseui-idp-text {
  color: #2C3E50 !important; /* Navy text */
  font-weight: 600 !important;
}

.firebaseui-idp-google:hover > .firebaseui-idp-text {
  color: #2C3E50 !important; /* Keep navy on hover */
}

/* Email button styling - Primary Cricket Green */
.firebaseui-idp-password {
  background: #27AE60 !important; /* Cricket Green */
  color: #FFFFFF !important; /* White text */
  border: 2px solid #27AE60 !important;
}

.firebaseui-idp-password:hover {
  background: #229954 !important; /* Darker green on hover */
  border-color: #229954 !important;
  transform: translateY(-1px) !important;
}

/* Input field styling - Cricket Theme */
.firebaseui-input {
  border: 2px solid #DEE2E6 !important; /* Light border */
  border-radius: 0.5rem !important;
  background: #FFFFFF !important; /* White background */
  color: #2C3E50 !important; /* Navy text */
  padding: 0.75rem 1rem !important;
  font-size: 1rem !important;
  height: 3rem !important;
  transition: all 0.2s ease-in-out !important;
}

.firebaseui-input:focus {
  border-color: #27AE60 !important; /* Cricket Green focus */
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1) !important;
  background: #FFFFFF !important;
}

.firebaseui-input::placeholder {
  color: #7F8C8D !important; /* Soft gray placeholder */
}

/* Label styling */
.firebaseui-label {
  color: #2C3E50 !important; /* Navy text */
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.5rem !important;
}

/* Error message styling */
.firebaseui-error {
  background: hsl(var(--destructive) / 0.1) !important;
  border: 1px solid hsl(var(--destructive) / 0.2) !important;
  color: hsl(var(--destructive)) !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
  margin: 1rem 0 !important;
}

/* Info message styling */
.firebaseui-info-bar {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
  margin: 1rem 0 !important;
}

/* Link styling */
.firebaseui-link {
  color: hsl(var(--primary)) !important;
  text-decoration: none !important;
}

.firebaseui-link:hover {
  text-decoration: underline !important;
}

/* Progress bar styling */
.firebaseui-busy-indicator {
  border-color: hsl(var(--primary)) transparent transparent transparent !important;
}

/* Form styling */
.firebaseui-form-actions {
  margin-top: 1.5rem !important;
}

.firebaseui-form-links {
  text-align: center !important;
  margin-top: 1rem !important;
}

/* Submit button styling */
.firebaseui-id-submit {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: none !important;
  border-radius: 0.375rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  height: 2.5rem !important;
  width: 100% !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
}

.firebaseui-id-submit:hover {
  opacity: 0.9 !important;
}

.firebaseui-id-submit:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* Secondary button styling */
.firebaseui-id-secondary-link {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.875rem !important;
}

/* Hide Firebase UI branding */
.firebaseui-tos {
  font-size: 0.75rem !important;
  color: hsl(var(--muted-foreground)) !important;
  text-align: center !important;
  margin-top: 1rem !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .firebaseui-container {
    padding: 0 !important;
  }
  
  .firebaseui-card-content {
    padding: 0 !important;
  }
}
