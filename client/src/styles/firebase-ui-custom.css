/* Custom Firebase UI Styles to match your application theme */

/* Container styling */
.firebase-ui-container {
  width: 100%;
}

/* Main container */
.firebaseui-container {
  background: transparent !important;
  box-shadow: none !important;
  max-width: none !important;
  width: 100% !important;
}

/* Card styling */
.firebaseui-card-content {
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
}

.firebaseui-card-header {
  padding: 0 0 1.5rem 0 !important;
  border-bottom: none !important;
}

.firebaseui-card-actions {
  padding: 1.5rem 0 0 0 !important;
}

/* Title styling */
.firebaseui-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
  text-align: center !important;
  margin: 0 0 0.5rem 0 !important;
}

.firebaseui-subtitle {
  color: hsl(var(--muted-foreground)) !important;
  text-align: center !important;
  font-size: 0.875rem !important;
  margin: 0 0 1.5rem 0 !important;
}

/* Button styling */
.firebaseui-idp-button {
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--border)) !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  font-weight: 500 !important;
  height: 2.5rem !important;
  margin: 0.5rem 0 !important;
  transition: all 0.2s ease-in-out !important;
}

.firebaseui-idp-button:hover {
  background: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

/* Google button specific styling */
.firebaseui-idp-google > .firebaseui-idp-text {
  color: hsl(var(--foreground)) !important;
}

/* Email button styling */
.firebaseui-idp-password {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: 1px solid hsl(var(--primary)) !important;
}

.firebaseui-idp-password:hover {
  background: hsl(var(--primary)) !important;
  opacity: 0.9 !important;
}

/* Input field styling */
.firebaseui-input {
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.375rem !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  height: 2.5rem !important;
}

.firebaseui-input:focus {
  border-color: hsl(var(--ring)) !important;
  outline: none !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2) !important;
}

/* Label styling */
.firebaseui-label {
  color: hsl(var(--foreground)) !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.5rem !important;
}

/* Error message styling */
.firebaseui-error {
  background: hsl(var(--destructive) / 0.1) !important;
  border: 1px solid hsl(var(--destructive) / 0.2) !important;
  color: hsl(var(--destructive)) !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
  margin: 1rem 0 !important;
}

/* Info message styling */
.firebaseui-info-bar {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem !important;
  font-size: 0.875rem !important;
  margin: 1rem 0 !important;
}

/* Link styling */
.firebaseui-link {
  color: hsl(var(--primary)) !important;
  text-decoration: none !important;
}

.firebaseui-link:hover {
  text-decoration: underline !important;
}

/* Progress bar styling */
.firebaseui-busy-indicator {
  border-color: hsl(var(--primary)) transparent transparent transparent !important;
}

/* Form styling */
.firebaseui-form-actions {
  margin-top: 1.5rem !important;
}

.firebaseui-form-links {
  text-align: center !important;
  margin-top: 1rem !important;
}

/* Submit button styling */
.firebaseui-id-submit {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: none !important;
  border-radius: 0.375rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  height: 2.5rem !important;
  width: 100% !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
}

.firebaseui-id-submit:hover {
  opacity: 0.9 !important;
}

.firebaseui-id-submit:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* Secondary button styling */
.firebaseui-id-secondary-link {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.875rem !important;
}

/* Hide Firebase UI branding */
.firebaseui-tos {
  font-size: 0.75rem !important;
  color: hsl(var(--muted-foreground)) !important;
  text-align: center !important;
  margin-top: 1rem !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .firebaseui-container {
    padding: 0 !important;
  }
  
  .firebaseui-card-content {
    padding: 0 !important;
  }
}
