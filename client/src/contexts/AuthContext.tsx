import React, { createContext, useContext, useEffect, useState } from 'react';
import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signInWithPopup, 
  GoogleAuthProvider, 
  signOut, 
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { User, UserRole } from '../types';
import { authService } from '../services/authService';

// Firebase configuration
const firebaseConfig = JSON.parse(import.meta.env.VITE_FIREBASE_CONFIG || '{}');

// Initialize Firebase (with error handling for demo mode)
let app: any = null;
let auth: any = null;
let googleProvider: any = null;

try {
  if (firebaseConfig.apiKey && firebaseConfig.apiKey !== 'test') {
    app = initializeApp(firebaseConfig);
    auth = getAuth(app);
    googleProvider = new GoogleAuthProvider();
    console.log('✅ Firebase initialized successfully');
  } else {
    console.warn('🚀 Running in demo mode - Firebase not configured');
  }
} catch (error) {
  console.warn('⚠️ Firebase initialization failed, running in demo mode:', error);
}

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if Firebase is configured
    if (!auth) {
      console.warn('Firebase not configured. Please check your Firebase configuration.');
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setFirebaseUser(firebaseUser);

      if (firebaseUser) {
        try {
          // Get ID token and authenticate with backend
          const idToken = await firebaseUser.getIdToken();
          const response = await authService.login(idToken);
          setUser(response.data?.user || null);
        } catch (error) {
          console.error('Authentication error:', error);
          setUser(null);
        }
      } else {
        setUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);



  const login = async (email: string, password: string) => {
    try {
      setLoading(true);

      // Demo users for development
      const demoUsers: Record<string, any> = {
        '<EMAIL>': {
          password: 'admin123',
          user: {
            id: 'demo-admin',
            email: '<EMAIL>',
            name: 'Admin User',
            role: 'ADMIN',
            phone: '+91 9876543210',
            avatarUrl: null
          }
        },
        '<EMAIL>': {
          password: 'house123',
          user: {
            id: 'demo-housekeeper',
            email: '<EMAIL>',
            name: 'Housekeeping Staff',
            role: 'HOUSEKEEPING',
            phone: '+91 9876543211',
            avatarUrl: null
          }
        },
        '<EMAIL>': {
          password: 'user123',
          user: {
            id: 'demo-user',
            email: '<EMAIL>',
            name: 'Regular User',
            role: 'USER',
            phone: '+91 9876543212',
            avatarUrl: null
          }
        }
      };

      const demoUser = demoUsers[email];
      if (!demoUser || demoUser.password !== password) {
        throw new Error('Invalid email or password');
      }

      // Store user in localStorage for demo mode
      localStorage.setItem('demo-user', JSON.stringify(demoUser.user));
      setUser(demoUser.user);
      setFirebaseUser({ uid: demoUser.user.id } as FirebaseUser);

    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    try {
      setLoading(true);

      if (!auth) {
        // Demo mode - simulate Google login
        const response = await authService.demoLogin('<EMAIL>', 'demo123');
        setUser(response.data?.user || null);
        setFirebaseUser({ uid: response.data?.user?.id || 'demo-google' } as FirebaseUser);
        return;
      }

      // Real Google login
      const result = await signInWithPopup(auth, googleProvider);
      const idToken = await result.user.getIdToken();
      const response = await authService.login(idToken);
      setUser(response.data?.user || null);
    } catch (error) {
      console.error('Google login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Clear demo user from localStorage
      localStorage.removeItem('demo-user');
      setUser(null);
      setFirebaseUser(null);

      if (auth) {
        // Firebase logout
        await authService.logout();
        await signOut(auth);
      }
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  const refreshUser = async () => {
    if (firebaseUser) {
      try {
        const response = await authService.getProfile();
        setUser(response.data?.user || null);
      } catch (error) {
        console.error('Refresh user error:', error);
      }
    }
  };

  // Initialize auth state
  useEffect(() => {
    // Check for demo user in localStorage first
    const storedDemoUser = localStorage.getItem('demo-user');
    if (storedDemoUser) {
      try {
        const demoUser = JSON.parse(storedDemoUser);
        setUser(demoUser);
        setFirebaseUser({ uid: demoUser.id } as FirebaseUser);
        setLoading(false);
        return;
      } catch (error) {
        console.error('Error parsing stored demo user:', error);
        localStorage.removeItem('demo-user');
      }
    }

    if (!auth) {
      console.warn('Firebase not configured - running in demo mode');
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setFirebaseUser(firebaseUser);

      if (firebaseUser) {
        try {
          const idToken = await firebaseUser.getIdToken();
          const response = await authService.login(idToken);
          setUser(response.data?.user || null);
        } catch (error) {
          console.error('Auth state change error:', error);
          setUser(null);
        }
      } else {
        setUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, [auth]);

  const value: AuthContextType = {
    user,
    firebaseUser,
    loading,
    login,
    loginWithGoogle,
    logout,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
