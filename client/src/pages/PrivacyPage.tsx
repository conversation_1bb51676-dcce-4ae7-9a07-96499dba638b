import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../components/ui/Card';

const PrivacyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center text-2xl font-bold text-gray-900 dark:text-white">
            <span className="text-3xl mr-2">🏏</span>
            Cricket Booking
          </Link>
          <h1 className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
            Privacy Policy
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Privacy Policy</CardTitle>
          </CardHeader>
          <CardContent className="prose dark:prose-invert max-w-none">
            <h2>1. Information We Collect</h2>
            <p>
              We collect information you provide directly to us, such as when you create an account, make a booking, or contact us for support.
            </p>
            <ul>
              <li><strong>Personal Information:</strong> Name, email address, phone number</li>
              <li><strong>Booking Information:</strong> Facility preferences, booking dates and times</li>
              <li><strong>Usage Information:</strong> How you interact with our system</li>
            </ul>

            <h2>2. How We Use Your Information</h2>
            <p>We use the information we collect to:</p>
            <ul>
              <li>Provide, maintain, and improve our services</li>
              <li>Process bookings and send confirmations</li>
              <li>Communicate with you about your account and bookings</li>
              <li>Send you technical notices and security alerts</li>
            </ul>

            <h2>3. Information Sharing</h2>
            <p>
              We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.
            </p>

            <h2>4. Data Security</h2>
            <p>
              We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
            </p>

            <h2>5. Firebase and Google Services</h2>
            <p>
              Our authentication system uses Firebase, which is governed by Google's Privacy Policy. When you sign in with Google, Google's privacy practices apply to the data they collect.
            </p>

            <h2>6. Your Rights</h2>
            <p>You have the right to:</p>
            <ul>
              <li>Access your personal information</li>
              <li>Correct inaccurate information</li>
              <li>Delete your account and associated data</li>
              <li>Opt out of certain communications</li>
            </ul>

            <h2>7. Contact Us</h2>
            <p>
              If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
            </p>
          </CardContent>
        </Card>

        <div className="mt-8 text-center">
          <Link
            to="/login"
            className="text-primary hover:text-primary/80 font-medium"
          >
            ← Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPage;
