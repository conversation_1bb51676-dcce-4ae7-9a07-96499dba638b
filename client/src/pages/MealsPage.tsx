import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import Button from '../components/ui/Button';

const MealsPage: React.FC = () => {
  const mealPlans = [
    {
      id: '1',
      date: '2025-06-07',
      mealType: 'BREAKFAST',
      menuItems: ['Tea/Coffee', 'Bread/Toast', 'Eggs', 'Fruits'],
      estimatedQuantity: 25,
      costPerPerson: 50,
      totalCost: 1250,
      isAutoGenerated: true
    },
    {
      id: '2',
      date: '2025-06-07',
      mealType: 'LUNCH',
      menuItems: ['Rice', 'Dal', 'Vegetables', 'Roti', 'Salad'],
      estimatedQuantity: 30,
      costPerPerson: 100,
      totalCost: 3000,
      isAutoGenerated: false
    },
    {
      id: '3',
      date: '2025-06-08',
      mealType: 'DINNER',
      menuItems: ['Rice', 'Dal', 'Chicken Curry', 'Roti', 'Dessert'],
      estimatedQuantity: 20,
      costPerPerson: 120,
      totalCost: 2400,
      isAutoGenerated: true
    }
  ];

  const getMealIcon = (mealType: string) => {
    switch (mealType) {
      case 'BREAKFAST': return '🌅';
      case 'LUNCH': return '🌞';
      case 'DINNER': return '🌙';
      case 'SNACKS': return '🍪';
      default: return '🍽️';
    }
  };

  const getMealColor = (mealType: string) => {
    switch (mealType) {
      case 'BREAKFAST': return 'bg-yellow-100 text-yellow-800';
      case 'LUNCH': return 'bg-green-100 text-green-800';
      case 'DINNER': return 'bg-blue-100 text-blue-800';
      case 'SNACKS': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Meal Planning</h1>
          <p className="text-muted-foreground">
            Plan and manage meals based on occupancy
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            🤖 Auto Generate
          </Button>
          <Button>
            ➕ Add Meal Plan
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">45</div>
            <p className="text-sm text-muted-foreground">Total Guests Today</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">₹6,650</div>
            <p className="text-sm text-muted-foreground">Today's Budget</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-orange-600">3</div>
            <p className="text-sm text-muted-foreground">Meals Planned</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-purple-600">₹148</div>
            <p className="text-sm text-muted-foreground">Avg Cost/Person</p>
          </CardContent>
        </Card>
      </div>

      {/* Date Filter */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">From:</label>
              <input
                type="date"
                defaultValue="2025-06-07"
                className="px-3 py-2 border rounded-md bg-background"
              />
            </div>
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">To:</label>
              <input
                type="date"
                defaultValue="2025-06-14"
                className="px-3 py-2 border rounded-md bg-background"
              />
            </div>
            <select className="px-3 py-2 border rounded-md bg-background">
              <option value="">All Meal Types</option>
              <option value="BREAKFAST">Breakfast</option>
              <option value="LUNCH">Lunch</option>
              <option value="DINNER">Dinner</option>
              <option value="SNACKS">Snacks</option>
            </select>
            <Button variant="outline">
              Apply Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Meal Plans */}
      <div className="space-y-4">
        {mealPlans.map((plan) => (
          <Card key={plan.id}>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-4 mb-3">
                    <span className="text-2xl">{getMealIcon(plan.mealType)}</span>
                    <div>
                      <h3 className="text-lg font-semibold">{plan.mealType}</h3>
                      <p className="text-sm text-muted-foreground">{plan.date}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${getMealColor(plan.mealType)}`}>
                      {plan.mealType}
                    </span>
                    {plan.isAutoGenerated && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        Auto Generated
                      </span>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div>
                      <span className="text-sm text-muted-foreground">Estimated Quantity:</span>
                      <div className="font-medium">{plan.estimatedQuantity} people</div>
                    </div>
                    
                    <div>
                      <span className="text-sm text-muted-foreground">Cost per Person:</span>
                      <div className="font-medium">₹{plan.costPerPerson}</div>
                    </div>
                    
                    <div>
                      <span className="text-sm text-muted-foreground">Total Cost:</span>
                      <div className="font-medium text-lg">₹{plan.totalCost.toLocaleString()}</div>
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-sm text-muted-foreground">Menu Items:</span>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {plan.menuItems.map((item, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-accent text-sm rounded-full"
                        >
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    Duplicate
                  </Button>
                  <Button variant="destructive" size="sm">
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Weekly Calendar View */}
      <Card>
        <CardHeader>
          <CardTitle>Weekly Meal Calendar</CardTitle>
          <CardDescription>
            Overview of planned meals for the week
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-2 mb-4">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
              <div key={day} className="text-center font-medium p-2 bg-muted rounded">
                {day}
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-7 gap-2">
            {Array.from({ length: 7 }, (_, i) => (
              <div key={i} className="border rounded-lg p-2 min-h-[120px]">
                <div className="text-sm font-medium mb-2">
                  {new Date(2025, 5, 7 + i).getDate()}
                </div>
                <div className="space-y-1">
                  <div className="text-xs p-1 bg-yellow-100 text-yellow-800 rounded">
                    🌅 Breakfast
                  </div>
                  <div className="text-xs p-1 bg-green-100 text-green-800 rounded">
                    🌞 Lunch
                  </div>
                  <div className="text-xs p-1 bg-blue-100 text-blue-800 rounded">
                    🌙 Dinner
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Auto Generation Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Auto Generation Settings</CardTitle>
          <CardDescription>
            Configure automatic meal plan generation based on bookings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Enable Auto Generation</h4>
                <p className="text-sm text-muted-foreground">
                  Automatically create meal plans based on confirmed bookings
                </p>
              </div>
              <input type="checkbox" defaultChecked className="h-4 w-4" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Default Breakfast Cost</label>
                <input
                  type="number"
                  defaultValue="50"
                  className="w-full px-3 py-2 border rounded-md bg-background mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Default Lunch Cost</label>
                <input
                  type="number"
                  defaultValue="100"
                  className="w-full px-3 py-2 border rounded-md bg-background mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Default Dinner Cost</label>
                <input
                  type="number"
                  defaultValue="120"
                  className="w-full px-3 py-2 border rounded-md bg-background mt-1"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Default Snacks Cost</label>
                <input
                  type="number"
                  defaultValue="30"
                  className="w-full px-3 py-2 border rounded-md bg-background mt-1"
                />
              </div>
            </div>
            
            <Button>
              Save Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MealsPage;
