import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AddFacilityModal from '../../components/admin/AddFacilityModal';
import { Plus, Edit, Trash2, MapPin, Users, Wifi, Car, Coffee, Tv, Building2, Search, Filter } from 'lucide-react';

interface Facility {
  id: string;
  name: string;
  type: 'CRICKET_GROUND' | 'BUILDING' | 'ROOM';
  description: string;
  capacity: number;
  location: string;
  amenities: string[];
  isActive: boolean;
  pricePerHour: number;
  images: string[];
  parentId?: string;
  createdAt: string;
}

const FacilityManagementPage: React.FC = () => {
  const [facilities, setFacilities] = useState<Facility[]>([
    {
      id: '1',
      name: 'Main Cricket Ground',
      type: 'CRICKET_GROUND',
      description: 'Professional cricket ground with full facilities',
      capacity: 50,
      location: 'North Wing',
      amenities: ['Floodlights', 'Pavilion', 'Scoreboard', 'Parking'],
      isActive: true,
      pricePerHour: 2000,
      images: [],
      createdAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      name: 'Sports Complex Building',
      type: 'BUILDING',
      description: 'Main building with multiple rooms and facilities',
      capacity: 200,
      location: 'Central Area',
      amenities: ['Elevator', 'AC', 'WiFi', 'Parking'],
      isActive: true,
      pricePerHour: 0,
      images: [],
      createdAt: '2024-01-10T09:00:00Z'
    },
    {
      id: '3',
      name: 'Conference Room A',
      type: 'ROOM',
      description: 'Large conference room with modern amenities',
      capacity: 25,
      location: 'Building 2, Floor 1',
      amenities: ['Projector', 'AC', 'WiFi', 'Whiteboard'],
      isActive: true,
      pricePerHour: 500,
      images: [],
      parentId: '2',
      createdAt: '2024-01-12T11:00:00Z'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('ALL');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingFacility, setEditingFacility] = useState<Facility | null>(null);

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi': return <Wifi className="w-4 h-4" />;
      case 'parking': case 'car': return <Car className="w-4 h-4" />;
      case 'ac': case 'air conditioning': return <Coffee className="w-4 h-4" />;
      case 'projector': case 'tv': return <Tv className="w-4 h-4" />;
      default: return <Building2 className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'CRICKET_GROUND': return 'bg-green-100 text-green-800';
      case 'BUILDING': return 'bg-blue-100 text-blue-800';
      case 'ROOM': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredFacilities = facilities.filter(facility => {
    const matchesSearch = facility.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         facility.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'ALL' || facility.type === filterType;
    return matchesSearch && matchesType;
  });

  const handleAddFacility = () => {
    setEditingFacility(null);
    setShowAddModal(true);
  };

  const handleEditFacility = (facilityId: string) => {
    const facility = facilities.find(f => f.id === facilityId);
    if (facility) {
      setEditingFacility(facility);
      setShowAddModal(true);
    }
  };

  const handleSaveFacility = (facilityData: Facility) => {
    if (editingFacility) {
      // Update existing facility
      setFacilities(prev => prev.map(f =>
        f.id === editingFacility.id ? { ...facilityData, id: editingFacility.id } : f
      ));
    } else {
      // Add new facility
      setFacilities(prev => [...prev, facilityData]);
    }
    setShowAddModal(false);
    setEditingFacility(null);
  };

  const handleDeleteFacility = (facilityId: string) => {
    if (window.confirm('Are you sure you want to delete this facility?')) {
      setFacilities(prev => prev.filter(f => f.id !== facilityId));
    }
  };

  const toggleFacilityStatus = (facilityId: string) => {
    setFacilities(prev => prev.map(f => 
      f.id === facilityId ? { ...f, isActive: !f.isActive } : f
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Facility Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage cricket grounds, buildings, and rooms
          </p>
        </div>
        <Button onClick={handleAddFacility} className="bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Add Facility
        </Button>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                placeholder="Search facilities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="ALL">All Types</option>
                <option value="CRICKET_GROUND">Cricket Grounds</option>
                <option value="BUILDING">Buildings</option>
                <option value="ROOM">Rooms</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Facilities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredFacilities.map((facility) => (
          <Card key={facility.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-lg">{facility.name}</CardTitle>
                    <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(facility.type)}`}>
                      {facility.type.replace('_', ' ')}
                    </span>
                  </div>
                  <CardDescription>{facility.description}</CardDescription>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditFacility(facility.id)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteFacility(facility.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Location and Capacity */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <MapPin className="w-4 h-4" />
                    {facility.location}
                  </div>
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Users className="w-4 h-4" />
                    {facility.capacity} people
                  </div>
                </div>

                {/* Price */}
                {facility.pricePerHour > 0 && (
                  <div className="text-lg font-semibold text-primary">
                    ₹{facility.pricePerHour}/hour
                  </div>
                )}

                {/* Amenities */}
                <div className="flex flex-wrap gap-2">
                  {facility.amenities.slice(0, 4).map((amenity, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-1 px-2 py-1 bg-muted rounded-md text-xs"
                    >
                      {getAmenityIcon(amenity)}
                      {amenity}
                    </div>
                  ))}
                  {facility.amenities.length > 4 && (
                    <div className="px-2 py-1 bg-muted rounded-md text-xs">
                      +{facility.amenities.length - 4} more
                    </div>
                  )}
                </div>

                {/* Status Toggle */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <button
                    onClick={() => toggleFacilityStatus(facility.id)}
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      facility.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {facility.isActive ? 'Active' : 'Inactive'}
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredFacilities.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No facilities found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || filterType !== 'ALL' 
                ? 'Try adjusting your search or filter criteria'
                : 'Get started by adding your first facility'
              }
            </p>
            {!searchTerm && filterType === 'ALL' && (
              <Button onClick={handleAddFacility}>
                <Plus className="w-4 h-4 mr-2" />
                Add First Facility
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-primary">
              {facilities.filter(f => f.type === 'CRICKET_GROUND').length}
            </div>
            <p className="text-sm text-muted-foreground">Cricket Grounds</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {facilities.filter(f => f.type === 'BUILDING').length}
            </div>
            <p className="text-sm text-muted-foreground">Buildings</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-purple-600">
              {facilities.filter(f => f.type === 'ROOM').length}
            </div>
            <p className="text-sm text-muted-foreground">Rooms</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {facilities.filter(f => f.isActive).length}
            </div>
            <p className="text-sm text-muted-foreground">Active Facilities</p>
          </CardContent>
        </Card>
      </div>

      {/* Add/Edit Facility Modal */}
      <AddFacilityModal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          setEditingFacility(null);
        }}
        onSave={handleSaveFacility}
        editingFacility={editingFacility}
      />
    </div>
  );
};

export default FacilityManagementPage;
