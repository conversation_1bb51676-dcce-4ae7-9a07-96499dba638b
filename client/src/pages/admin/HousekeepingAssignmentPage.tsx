import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { Plus, Edit, Trash2, User, Clock, AlertTriangle, CheckCircle, XCircle, Calendar, Filter, Search } from 'lucide-react';

interface HousekeepingTask {
  id: string;
  facilityId: string;
  facilityName: string;
  facilityType: string;
  assignedTo: string;
  assignedToName: string;
  taskType: 'CLEANING' | 'MAINTENANCE' | 'INSPECTION' | 'SETUP';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'NEEDS_REPAIR';
  scheduledDate: string;
  deadline: string;
  description: string;
  instructions?: string;
  estimatedDuration: number; // in minutes
  completedAt?: string;
  images: string[];
  notes?: string;
  createdAt: string;
}

interface Staff {
  id: string;
  name: string;
  email: string;
  phone: string;
  specialties: string[];
  isActive: boolean;
  currentTasks: number;
}

const HousekeepingAssignmentPage: React.FC = () => {
  const [tasks, setTasks] = useState<HousekeepingTask[]>([
    {
      id: '1',
      facilityId: '3',
      facilityName: 'Conference Room A',
      facilityType: 'ROOM',
      assignedTo: 'staff1',
      assignedToName: 'Alice Johnson',
      taskType: 'CLEANING',
      priority: 'HIGH',
      status: 'PENDING',
      scheduledDate: '2024-01-20T09:00:00Z',
      deadline: '2024-01-20T11:00:00Z',
      description: 'Deep cleaning after conference',
      instructions: 'Pay special attention to whiteboard and projector area',
      estimatedDuration: 90,
      images: [],
      createdAt: '2024-01-19T10:00:00Z'
    },
    {
      id: '2',
      facilityId: '1',
      facilityName: 'Main Cricket Ground',
      facilityType: 'CRICKET_GROUND',
      assignedTo: 'staff2',
      assignedToName: 'Bob Smith',
      taskType: 'MAINTENANCE',
      priority: 'MEDIUM',
      status: 'IN_PROGRESS',
      scheduledDate: '2024-01-20T14:00:00Z',
      deadline: '2024-01-20T17:00:00Z',
      description: 'Pitch maintenance and line marking',
      estimatedDuration: 180,
      images: [],
      createdAt: '2024-01-19T11:30:00Z'
    }
  ]);

  const [staff, setStaff] = useState<Staff[]>([
    {
      id: 'staff1',
      name: 'Alice Johnson',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      specialties: ['Room Cleaning', 'Event Setup'],
      isActive: true,
      currentTasks: 2
    },
    {
      id: 'staff2',
      name: 'Bob Smith',
      email: '<EMAIL>',
      phone: '+91 9876543211',
      specialties: ['Ground Maintenance', 'Equipment Care'],
      isActive: true,
      currentTasks: 1
    },
    {
      id: 'staff3',
      name: 'Carol Davis',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      specialties: ['Deep Cleaning', 'Maintenance'],
      isActive: true,
      currentTasks: 0
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [priorityFilter, setPriorityFilter] = useState<string>('ALL');
  const [showAssignModal, setShowAssignModal] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'bg-green-100 text-green-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'URGENT': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-gray-100 text-gray-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'NEEDS_REPAIR': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <Clock className="w-4 h-4" />;
      case 'IN_PROGRESS': return <AlertTriangle className="w-4 h-4" />;
      case 'COMPLETED': return <CheckCircle className="w-4 h-4" />;
      case 'NEEDS_REPAIR': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.facilityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || task.status === statusFilter;
    const matchesPriority = priorityFilter === 'ALL' || task.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const handleStatusChange = (taskId: string, newStatus: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId 
        ? { 
            ...task, 
            status: newStatus as any,
            completedAt: newStatus === 'COMPLETED' ? new Date().toISOString() : task.completedAt
          } 
        : task
    ));
  };

  const handleDeleteTask = (taskId: string) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      setTasks(prev => prev.filter(task => task.id !== taskId));
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const isOverdue = (deadline: string, status: string) => {
    return status !== 'COMPLETED' && new Date(deadline) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Housekeeping Assignment</h1>
          <p className="text-muted-foreground mt-2">
            Assign and manage housekeeping tasks for facilities
          </p>
        </div>
        <Button onClick={() => setShowAssignModal(true)} className="bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Assign Task
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="ALL">All Status</option>
                <option value="PENDING">Pending</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="COMPLETED">Completed</option>
                <option value="NEEDS_REPAIR">Needs Repair</option>
              </select>
              <select
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="ALL">All Priority</option>
                <option value="URGENT">Urgent</option>
                <option value="HIGH">High</option>
                <option value="MEDIUM">Medium</option>
                <option value="LOW">Low</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-gray-600">
              {tasks.filter(t => t.status === 'PENDING').length}
            </div>
            <p className="text-sm text-muted-foreground">Pending Tasks</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {tasks.filter(t => t.status === 'IN_PROGRESS').length}
            </div>
            <p className="text-sm text-muted-foreground">In Progress</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-red-600">
              {tasks.filter(t => isOverdue(t.deadline, t.status)).length}
            </div>
            <p className="text-sm text-muted-foreground">Overdue</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {tasks.filter(t => t.status === 'COMPLETED').length}
            </div>
            <p className="text-sm text-muted-foreground">Completed</p>
          </CardContent>
        </Card>
      </div>

      {/* Staff Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Staff Overview</CardTitle>
          <CardDescription>Current workload and availability</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {staff.filter(s => s.isActive).map(member => (
              <div key={member.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{member.name}</h4>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    member.currentTasks === 0 ? 'bg-green-100 text-green-800' :
                    member.currentTasks <= 2 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {member.currentTasks} tasks
                  </span>
                </div>
                <p className="text-sm text-muted-foreground mb-2">{member.email}</p>
                <div className="flex flex-wrap gap-1">
                  {member.specialties.map(specialty => (
                    <span key={specialty} className="px-2 py-1 bg-muted text-xs rounded">
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tasks List */}
      <div className="space-y-4">
        {filteredTasks.map((task) => {
          const scheduledDateTime = formatDateTime(task.scheduledDate);
          const deadlineDateTime = formatDateTime(task.deadline);
          const overdue = isOverdue(task.deadline, task.status);

          return (
            <Card key={task.id} className={`hover:shadow-md transition-shadow ${overdue ? 'border-red-200' : ''}`}>
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2 flex-wrap">
                      <h3 className="font-semibold text-lg">{task.facilityName}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(task.status)}`}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(task.status)}
                          {task.status.replace('_', ' ')}
                        </div>
                      </span>
                      {overdue && (
                        <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                          OVERDUE
                        </span>
                      )}
                    </div>
                    
                    <p className="text-muted-foreground">{task.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        {task.assignedToName}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {scheduledDateTime.date} • {scheduledDateTime.time}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        Due: {deadlineDateTime.time} ({task.estimatedDuration}min)
                      </div>
                    </div>

                    {task.instructions && (
                      <p className="text-sm text-muted-foreground italic">
                        Instructions: {task.instructions}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {task.status === 'PENDING' && (
                      <Button
                        size="sm"
                        onClick={() => handleStatusChange(task.id, 'IN_PROGRESS')}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Start
                      </Button>
                    )}
                    
                    {task.status === 'IN_PROGRESS' && (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleStatusChange(task.id, 'COMPLETED')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Complete
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusChange(task.id, 'NEEDS_REPAIR')}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <XCircle className="w-4 h-4 mr-1" />
                          Issue
                        </Button>
                      </>
                    )}
                    
                    <Button variant="ghost" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteTask(task.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}

        {filteredTasks.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No tasks found</h3>
              <p className="text-muted-foreground">
                {searchTerm || statusFilter !== 'ALL' || priorityFilter !== 'ALL'
                  ? 'Try adjusting your search or filter criteria'
                  : 'No housekeeping tasks have been assigned yet'
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default HousekeepingAssignmentPage;
