import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import BookingCalendar from '../../components/admin/BookingCalendar';
import { Calendar, Grid, Filter, Search, Plus, Edit, Trash2, Check, X, Clock, Users, MapPin } from 'lucide-react';

interface Booking {
  id: string;
  facilityId: string;
  facilityName: string;
  facilityType: string;
  userId: string;
  userName: string;
  userEmail: string;
  startTime: string;
  endTime: string;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
  guestCount: number;
  totalAmount: number;
  specialRequests?: string;
  createdAt: string;
}

const BookingManagementPage: React.FC = () => {
  const [bookings, setBookings] = useState<Booking[]>([
    {
      id: '1',
      facilityId: '1',
      facilityName: 'Main Cricket Ground',
      facilityType: 'CRICKET_GROUND',
      userId: '1',
      userName: '<PERSON>',
      userEmail: '<EMAIL>',
      startTime: '2024-01-20T10:00:00Z',
      endTime: '2024-01-20T14:00:00Z',
      status: 'PENDING',
      guestCount: 22,
      totalAmount: 8000,
      specialRequests: 'Need scoreboard operator',
      createdAt: '2024-01-15T09:00:00Z'
    },
    {
      id: '2',
      facilityId: '3',
      facilityName: 'Conference Room A',
      facilityType: 'ROOM',
      userId: '2',
      userName: 'Jane Smith',
      userEmail: '<EMAIL>',
      startTime: '2024-01-21T09:00:00Z',
      endTime: '2024-01-21T12:00:00Z',
      status: 'CONFIRMED',
      guestCount: 15,
      totalAmount: 1500,
      createdAt: '2024-01-16T10:30:00Z'
    },
    {
      id: '3',
      facilityId: '1',
      facilityName: 'Main Cricket Ground',
      facilityType: 'CRICKET_GROUND',
      userId: '3',
      userName: 'Mike Johnson',
      userEmail: '<EMAIL>',
      startTime: '2024-01-22T15:00:00Z',
      endTime: '2024-01-22T18:00:00Z',
      status: 'COMPLETED',
      guestCount: 30,
      totalAmount: 6000,
      createdAt: '2024-01-17T14:15:00Z'
    }
  ]);

  const [viewMode, setViewMode] = useState<'grid' | 'calendar'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [dateFilter, setDateFilter] = useState<string>('ALL');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'CONFIRMED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      case 'COMPLETED': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <Clock className="w-4 h-4" />;
      case 'CONFIRMED': return <Check className="w-4 h-4" />;
      case 'CANCELLED': return <X className="w-4 h-4" />;
      case 'COMPLETED': return <Check className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.facilityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.userEmail.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || booking.status === statusFilter;
    
    let matchesDate = true;
    if (dateFilter !== 'ALL') {
      const bookingDate = new Date(booking.startTime);
      const today = new Date();
      
      switch (dateFilter) {
        case 'TODAY':
          matchesDate = bookingDate.toDateString() === today.toDateString();
          break;
        case 'TOMORROW':
          const tomorrow = new Date(today);
          tomorrow.setDate(today.getDate() + 1);
          matchesDate = bookingDate.toDateString() === tomorrow.toDateString();
          break;
        case 'THIS_WEEK':
          const weekStart = new Date(today);
          weekStart.setDate(today.getDate() - today.getDay());
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          matchesDate = bookingDate >= weekStart && bookingDate <= weekEnd;
          break;
      }
    }
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const handleStatusChange = (bookingId: string, newStatus: string) => {
    setBookings(prev => prev.map(booking =>
      booking.id === bookingId ? { ...booking, status: newStatus as any } : booking
    ));
  };

  const handleDeleteBooking = (bookingId: string) => {
    if (window.confirm('Are you sure you want to delete this booking?')) {
      setBookings(prev => prev.filter(booking => booking.id !== bookingId));
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const formatDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const hours = Math.abs(end.getTime() - start.getTime()) / (1000 * 60 * 60);
    return `${hours}h`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Booking Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage and track all facility bookings
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            onClick={() => setViewMode('grid')}
          >
            <Grid className="w-4 h-4 mr-2" />
            Grid
          </Button>
          <Button
            variant={viewMode === 'calendar' ? 'default' : 'outline'}
            onClick={() => setViewMode('calendar')}
          >
            <Calendar className="w-4 h-4 mr-2" />
            Calendar
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                placeholder="Search bookings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="ALL">All Status</option>
                <option value="PENDING">Pending</option>
                <option value="CONFIRMED">Confirmed</option>
                <option value="CANCELLED">Cancelled</option>
                <option value="COMPLETED">Completed</option>
              </select>
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="ALL">All Dates</option>
                <option value="TODAY">Today</option>
                <option value="TOMORROW">Tomorrow</option>
                <option value="THIS_WEEK">This Week</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-yellow-600">
              {bookings.filter(b => b.status === 'PENDING').length}
            </div>
            <p className="text-sm text-muted-foreground">Pending Approval</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {bookings.filter(b => b.status === 'CONFIRMED').length}
            </div>
            <p className="text-sm text-muted-foreground">Confirmed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {bookings.filter(b => b.status === 'COMPLETED').length}
            </div>
            <p className="text-sm text-muted-foreground">Completed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-primary">
              ₹{bookings.reduce((sum, b) => sum + b.totalAmount, 0).toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">Total Revenue</p>
          </CardContent>
        </Card>
      </div>

      {/* Bookings Content */}
      {viewMode === 'grid' ? (
        <div className="space-y-4">
          {filteredBookings.map((booking) => {
            const startDateTime = formatDateTime(booking.startTime);
            const endDateTime = formatDateTime(booking.endTime);
            const duration = formatDuration(booking.startTime, booking.endTime);

            return (
              <Card key={booking.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-lg">{booking.facilityName}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(booking.status)}`}>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(booking.status)}
                            {booking.status}
                          </div>
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          {booking.userName} ({booking.guestCount} guests)
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {startDateTime.date} • {startDateTime.time} - {endDateTime.time} ({duration})
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="font-semibold text-primary">₹{booking.totalAmount.toLocaleString()}</span>
                        </div>
                      </div>

                      {booking.specialRequests && (
                        <p className="text-sm text-muted-foreground italic">
                          Special requests: {booking.specialRequests}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {booking.status === 'PENDING' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleStatusChange(booking.id, 'CONFIRMED')}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <Check className="w-4 h-4 mr-1" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStatusChange(booking.id, 'CANCELLED')}
                            className="text-red-600 border-red-600 hover:bg-red-50"
                          >
                            <X className="w-4 h-4 mr-1" />
                            Reject
                          </Button>
                        </>
                      )}
                      
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBooking(booking.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {filteredBookings.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No bookings found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || statusFilter !== 'ALL' || dateFilter !== 'ALL'
                    ? 'Try adjusting your search or filter criteria'
                    : 'No bookings have been made yet'
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        <BookingCalendar
          bookings={bookings}
          onEventSelect={(event) => {
            console.log('Selected event:', event);
            // Handle event selection (e.g., show details modal)
          }}
          onSlotSelect={(slotInfo) => {
            console.log('Selected slot:', slotInfo);
            // Handle slot selection (e.g., create new booking)
          }}
          onEventUpdate={(event) => {
            console.log('Updated event:', event);
            // Handle event update (e.g., drag and drop)
          }}
        />
      )}
    </div>
  );
};

export default BookingManagementPage;
