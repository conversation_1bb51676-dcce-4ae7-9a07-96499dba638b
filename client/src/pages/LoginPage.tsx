import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import FirebaseUIAuth from '../components/auth/FirebaseUIAuth';
import '../styles/firebase-ui-custom.css';

const LoginPage: React.FC = () => {
  const [error, setError] = useState('');

  const { user } = useAuth();
  const navigate = useNavigate();

  // Redirect if already logged in
  React.useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleFirebaseUISignInSuccess = async (user: any) => {
    try {
      // Get ID token and authenticate with backend
      const idToken = await user.getIdToken();
      // The AuthContext will handle the authentication automatically
      navigate('/dashboard');
    } catch (error: any) {
      setError(error.message || 'Authentication failed. Please try again.');
    }
  };

  const handleFirebaseUISignInFailure = (error: any) => {
    setError(error.message || 'Sign-in failed. Please try again.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 to-accent/5 dark:from-secondary dark:to-secondary/80 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center text-3xl font-bold text-primary hover:text-primary/80 transition-colors">
            <span className="text-4xl mr-3">🏏</span>
            Cricket Ground
          </Link>
          <p className="mt-3 text-lg font-medium text-secondary dark:text-muted">
            Book Your Perfect Match
          </p>
          <p className="mt-1 text-muted-foreground">
            Sign in to access cricket grounds and facilities
          </p>
        </div>

        <Card className="shadow-xl border-0 bg-card/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-2">
            <CardTitle className="text-2xl font-bold text-primary">Welcome to Cricket Ground</CardTitle>
            <CardDescription className="text-muted-foreground">
              Sign in or create an account to book cricket facilities
            </CardDescription>
          </CardHeader>

          <CardContent className="pt-6">
            {error && (
              <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <p className="text-sm text-destructive font-medium">{error}</p>
              </div>
            )}

            {/* Cricket-themed info banner */}
            <div className="mb-6 p-4 bg-primary/10 border border-primary/20 rounded-lg">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-2">🏏</span>
                <h4 className="text-sm font-semibold text-primary">
                  Join the Cricket Community
                </h4>
              </div>
              <p className="text-xs text-primary/80">
                Access premium cricket grounds, book facilities, and manage your cricket activities all in one place.
              </p>
            </div>

            {/* Firebase UI Authentication */}
            <div className="firebase-ui-auth-container">
              <FirebaseUIAuth
                onSignInSuccess={handleFirebaseUISignInSuccess}
                onSignInFailure={handleFirebaseUISignInFailure}
              />
            </div>

            <div className="mt-8 text-center space-y-4">
              <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
                <span>🏟️ Premium Grounds</span>
                <span>•</span>
                <span>⚡ Instant Booking</span>
                <span>•</span>
                <span>🎯 Easy Management</span>
              </div>

              <Link
                to="/"
                className="inline-flex items-center text-sm text-primary hover:text-primary/80 font-medium transition-colors"
              >
                ← Back to Home
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
